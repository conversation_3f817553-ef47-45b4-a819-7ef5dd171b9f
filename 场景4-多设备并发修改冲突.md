# 场景4：多设备并发修改冲突

## 📱 场景描述

**用户情况**：
- 用户在多个设备上同时工作（如电脑、手机、平板）
- 在短时间内对同一条数据进行修改
- 网络延迟导致冲突检测滞后
- 需要实时解决数据冲突并保持一致性

**典型场景**：
- 在电脑上修改服务器IP，同时在手机上修改同一服务器的端口
- 团队成员同时编辑同一资产的不同字段
- 网络不稳定导致的同步延迟冲突
- 快速连续的修改操作

**核心需求**：
1. 实时检测并发修改冲突
2. 智能合并非冲突字段
3. 提供直观的冲突解决界面
4. 保证最终数据一致性
5. 最小化用户干预

## 🎯 解决方案

### 1. 实时冲突检测机制

#### 乐观锁机制
```python
class OptimisticLockManager:
    def __init__(self, db):
        self.db = db
        
    async def acquire_record_lock(self, record_id, operation_type):
        """获取记录的乐观锁"""
        
        # 获取当前记录版本
        current_record = await self.db.fetch_one("""
            SELECT sync_version, updated_at, device_id 
            FROM t_assets WHERE uuid = ?
        """, (record_id,))
        
        if not current_record:
            return None
            
        lock_info = {
            'record_id': record_id,
            'version': current_record['sync_version'],
            'last_updated': current_record['updated_at'],
            'operation_type': operation_type,
            'acquired_at': datetime.now(),
            'device_id': self.device_id
        }
        
        return lock_info
        
    async def validate_lock_before_commit(self, lock_info, new_data):
        """提交前验证锁的有效性"""
        
        # 重新获取记录当前状态
        current_record = await self.db.fetch_one("""
            SELECT sync_version, updated_at, device_id 
            FROM t_assets WHERE uuid = ?
        """, (lock_info['record_id'],))
        
        # 检查版本是否发生变化
        if current_record['sync_version'] != lock_info['version']:
            # 版本冲突，需要处理
            return {
                'valid': False,
                'conflict_type': 'VERSION_CONFLICT',
                'expected_version': lock_info['version'],
                'current_version': current_record['sync_version'],
                'current_data': current_record
            }
        
        return {'valid': True}
```

#### 实时冲突监听
```python
class RealTimeConflictDetector:
    def __init__(self, websocket_client):
        self.ws_client = websocket_client
        self.active_edits = {}  # 记录当前正在编辑的记录
        
    async def start_edit_session(self, record_id):
        """开始编辑会话"""
        
        # 通知服务器开始编辑
        await self.ws_client.send({
            'type': 'start_edit',
            'record_id': record_id,
            'device_id': self.device_id,
            'timestamp': datetime.now().isoformat()
        })
        
        # 记录本地编辑状态
        self.active_edits[record_id] = {
            'start_time': datetime.now(),
            'original_data': await self.get_record_data(record_id)
        }
        
    async def on_concurrent_edit_detected(self, message):
        """处理检测到的并发编辑"""
        
        record_id = message['record_id']
        other_device = message['device_id']
        
        if record_id in self.active_edits:
            # 显示并发编辑警告
            await self.show_concurrent_edit_warning(record_id, other_device)
            
    async def show_concurrent_edit_warning(self, record_id, other_device):
        """显示并发编辑警告"""
        
        warning_ui = f"""
        ⚠️ 并发编辑检测
        
        其他设备 ({other_device}) 正在编辑同一条记录。
        
        建议：
        • 与团队成员协调编辑时间
        • 完成编辑后立即保存
        • 注意可能的数据冲突
        
        [ 继续编辑 ]  [ 查看其他设备修改 ]
        """
        
        await self.show_notification(warning_ui)
```

### 2. 智能字段级合并

#### 字段级冲突检测
```python
class FieldLevelConflictResolver:
    def __init__(self):
        self.field_merge_rules = {
            'label': 'prefer_longer',           # 名称：选择更长的
            'asset_ip': 'prefer_valid_ip',      # IP：选择有效IP
            'port': 'prefer_standard_port',     # 端口：选择标准端口
            'favorite': 'prefer_true',          # 收藏：优先True
            'group_name': 'prefer_non_empty',   # 分组：优先非空
            'username': 'prefer_non_empty',     # 用户名：优先非空
            'password': 'keep_encrypted',       # 密码：保持加密状态
            'updated_at': 'use_latest'          # 更新时间：使用最新
        }
        
    async def detect_field_conflicts(self, local_data, remote_data):
        """检测字段级冲突"""
        
        conflicts = {}
        auto_mergeable = {}
        
        for field in local_data.keys():
            local_val = local_data.get(field)
            remote_val = remote_data.get(field)
            
            if local_val != remote_val:
                merge_rule = self.field_merge_rules.get(field, 'manual')
                
                if merge_rule == 'manual':
                    # 需要手动处理的冲突
                    conflicts[field] = {
                        'local': local_val,
                        'remote': remote_val,
                        'rule': merge_rule
                    }
                else:
                    # 可以自动合并的字段
                    merged_val = await self.apply_merge_rule(
                        field, local_val, remote_val, merge_rule
                    )
                    auto_mergeable[field] = {
                        'local': local_val,
                        'remote': remote_val,
                        'merged': merged_val,
                        'rule': merge_rule
                    }
        
        return {
            'manual_conflicts': conflicts,
            'auto_mergeable': auto_mergeable
        }
        
    async def apply_merge_rule(self, field, local_val, remote_val, rule):
        """应用合并规则"""
        
        if rule == 'prefer_longer':
            return local_val if len(str(local_val)) > len(str(remote_val)) else remote_val
            
        elif rule == 'prefer_valid_ip':
            if self.is_valid_ip(local_val):
                return local_val
            elif self.is_valid_ip(remote_val):
                return remote_val
            else:
                return local_val  # 都无效时保持本地
                
        elif rule == 'prefer_standard_port':
            standard_ports = [22, 80, 443, 3389, 3306, 5432]
            if local_val in standard_ports:
                return local_val
            elif remote_val in standard_ports:
                return remote_val
            else:
                return remote_val  # 都非标准时使用远程
                
        elif rule == 'prefer_true':
            return local_val or remote_val
            
        elif rule == 'prefer_non_empty':
            return local_val if local_val else remote_val
            
        elif rule == 'use_latest':
            # 对于时间字段，使用最新的
            try:
                local_time = datetime.fromisoformat(str(local_val))
                remote_time = datetime.fromisoformat(str(remote_val))
                return local_val if local_time > remote_time else remote_val
            except:
                return remote_val
                
        return local_val  # 默认保持本地值
```

#### 智能合并算法
```python
async def smart_merge_records(self, local_record, remote_record):
    """智能合并记录"""
    
    # 1. 检测字段级冲突
    conflict_analysis = await self.detect_field_conflicts(local_record, remote_record)
    
    # 2. 构建合并结果
    merged_record = local_record.copy()
    
    # 3. 应用自动合并的字段
    for field, merge_info in conflict_analysis['auto_mergeable'].items():
        merged_record[field] = merge_info['merged']
    
    # 4. 处理手动冲突字段
    manual_conflicts = conflict_analysis['manual_conflicts']
    
    if manual_conflicts:
        # 需要用户介入
        user_resolutions = await self.get_user_conflict_resolutions(manual_conflicts)
        
        for field, resolution in user_resolutions.items():
            merged_record[field] = resolution['chosen_value']
    
    # 5. 更新元数据
    merged_record['sync_version'] = max(
        local_record.get('sync_version', 1),
        remote_record.get('sync_version', 1)
    ) + 1
    merged_record['updated_at'] = datetime.now().isoformat()
    merged_record['device_id'] = self.device_id
    
    return {
        'merged_record': merged_record,
        'auto_merged_fields': list(conflict_analysis['auto_mergeable'].keys()),
        'manual_resolved_fields': list(manual_conflicts.keys()),
        'merge_summary': self.generate_merge_summary(conflict_analysis)
    }
```

### 3. 冲突解决用户界面

#### 直观的冲突对比界面
```python
class ConflictResolutionUI:
    async def show_conflict_resolution_dialog(self, conflicts):
        """显示冲突解决对话框"""
        
        ui_layout = self.build_conflict_ui(conflicts)
        
        return await self.show_modal_dialog(ui_layout)
    
    def build_conflict_ui(self, conflicts):
        """构建冲突解决UI"""
        
        ui_components = []
        
        for field, conflict_info in conflicts['manual_conflicts'].items():
            field_ui = self.create_field_conflict_component(field, conflict_info)
            ui_components.append(field_ui)
        
        # 添加自动合并预览
        if conflicts['auto_mergeable']:
            auto_merge_preview = self.create_auto_merge_preview(conflicts['auto_mergeable'])
            ui_components.append(auto_merge_preview)
        
        return {
            'title': '数据冲突解决',
            'components': ui_components,
            'actions': [
                {'id': 'apply_all', 'text': '应用所有选择', 'style': 'primary'},
                {'id': 'cancel', 'text': '取消', 'style': 'secondary'},
                {'id': 'preview', 'text': '预览合并结果', 'style': 'info'}
            ]
        }
    
    def create_field_conflict_component(self, field, conflict_info):
        """创建字段冲突组件"""
        
        return {
            'type': 'field_conflict',
            'field_name': field,
            'field_label': self.get_field_display_name(field),
            'local_value': conflict_info['local'],
            'remote_value': conflict_info['remote'],
            'options': [
                {'value': 'local', 'label': f'使用本地值: {conflict_info["local"]}'},
                {'value': 'remote', 'label': f'使用远程值: {conflict_info["remote"]}'},
                {'value': 'custom', 'label': '自定义输入'}
            ],
            'default_selection': self.suggest_default_choice(field, conflict_info)
        }
```

#### 实时预览功能
```python
async def show_merge_preview(self, local_record, remote_record, user_choices):
    """显示合并预览"""
    
    # 根据用户选择生成预览
    preview_record = await self.generate_preview_record(
        local_record, remote_record, user_choices
    )
    
    preview_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                      合并结果预览                            │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  字段名称        │  原本地值    │  原远程值    │  合并后值    │
    │  ─────────────────────────────────────────────────────────  │
    │  资产名称        │  生产服务器  │  主服务器    │  生产服务器  │ ✓
    │  IP地址          │  *********** │  *********** │  *********** │ ⚠️
    │  端口            │  22          │  2222        │  22          │ ✓
    │  收藏状态        │  否          │  是          │  是          │ ✓
    │                                                             │
    │  ✓ 自动合并  ⚠️ 用户选择  🔄 智能合并                      │
    │                                                             │
    │  [ 确认合并 ]  [ 修改选择 ]  [ 取消 ]                      │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_preview_dialog(preview_ui, preview_record)
```

### 4. 冲突预防机制

#### 编辑锁定机制
```python
class EditLockManager:
    def __init__(self, websocket_client):
        self.ws_client = websocket_client
        self.locked_records = set()
        
    async def request_edit_lock(self, record_id):
        """请求编辑锁"""
        
        if record_id in self.locked_records:
            return {'success': True, 'already_locked': True}
        
        # 向服务器请求锁
        response = await self.ws_client.send_and_wait({
            'type': 'request_edit_lock',
            'record_id': record_id,
            'device_id': self.device_id,
            'timeout': 300  # 5分钟超时
        })
        
        if response['success']:
            self.locked_records.add(record_id)
            
            # 启动锁心跳
            asyncio.create_task(self.maintain_lock_heartbeat(record_id))
            
        return response
        
    async def release_edit_lock(self, record_id):
        """释放编辑锁"""
        
        if record_id not in self.locked_records:
            return
        
        await self.ws_client.send({
            'type': 'release_edit_lock',
            'record_id': record_id,
            'device_id': self.device_id
        })
        
        self.locked_records.discard(record_id)
        
    async def maintain_lock_heartbeat(self, record_id):
        """维护锁心跳"""
        
        while record_id in self.locked_records:
            await asyncio.sleep(30)  # 每30秒发送心跳
            
            if record_id in self.locked_records:
                await self.ws_client.send({
                    'type': 'lock_heartbeat',
                    'record_id': record_id,
                    'device_id': self.device_id
                })
```

#### 协作提示机制
```python
class CollaborationNotifier:
    async def notify_concurrent_editing(self, record_id, other_devices):
        """通知并发编辑情况"""
        
        device_names = [self.get_device_name(device_id) for device_id in other_devices]
        
        notification = {
            'type': 'concurrent_edit_warning',
            'title': '协作编辑提醒',
            'message': f'设备 {", ".join(device_names)} 正在编辑同一条记录',
            'actions': [
                {'id': 'continue', 'text': '继续编辑'},
                {'id': 'view_others', 'text': '查看其他编辑'},
                {'id': 'coordinate', 'text': '协调编辑'}
            ],
            'auto_dismiss': False
        }
        
        await self.show_notification(notification)
        
    async def show_edit_coordination_dialog(self, record_id, other_devices):
        """显示编辑协调对话框"""
        
        coordination_ui = f"""
        ┌─────────────────────────────────────────────────────────────┐
        │                    编辑协调                                  │
        ├─────────────────────────────────────────────────────────────┤
        │                                                             │
        │  当前正在编辑此记录的设备：                                  │
        │                                                             │
        │  🖥️  John的MacBook Pro    (正在编辑IP地址)                 │
        │  📱  Mary的iPhone         (正在编辑端口)                    │
        │  💻  你的设备             (准备编辑)                        │
        │                                                             │
        │  建议的协调方案：                                            │
        │  • 等待其他设备完成编辑                                      │
        │  • 编辑不同的字段避免冲突                                    │
        │  • 发送协调消息给其他用户                                    │
        │                                                             │
        │  [ 等待完成 ]  [ 编辑其他字段 ]  [ 发送消息 ]  [ 强制编辑 ] │
        └─────────────────────────────────────────────────────────────┘
        """
        
        return await self.show_coordination_dialog(coordination_ui)
```

### 5. 冲突历史与学习

#### 冲突模式学习
```python
class ConflictPatternLearner:
    def __init__(self, db):
        self.db = db
        
    async def record_conflict_resolution(self, conflict_info, resolution):
        """记录冲突解决方案"""
        
        pattern = {
            'field_name': conflict_info['field'],
            'local_value_type': self.classify_value_type(conflict_info['local']),
            'remote_value_type': self.classify_value_type(conflict_info['remote']),
            'resolution_strategy': resolution['strategy'],
            'chosen_value': resolution['chosen_value'],
            'user_id': self.user_id,
            'device_id': self.device_id,
            'timestamp': datetime.now()
        }
        
        await self.db.execute("""
            INSERT INTO conflict_resolution_patterns 
            (field_name, local_type, remote_type, strategy, user_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            pattern['field_name'],
            pattern['local_value_type'],
            pattern['remote_value_type'], 
            pattern['resolution_strategy'],
            pattern['user_id'],
            pattern['timestamp']
        ))
        
    async def suggest_resolution_based_on_history(self, conflict_info):
        """基于历史记录建议解决方案"""
        
        # 查找相似的历史冲突
        similar_conflicts = await self.db.fetch_all("""
            SELECT strategy, COUNT(*) as frequency
            FROM conflict_resolution_patterns
            WHERE field_name = ? AND user_id = ?
            GROUP BY strategy
            ORDER BY frequency DESC, created_at DESC
            LIMIT 3
        """, (conflict_info['field'], self.user_id))
        
        if similar_conflicts:
            most_common = similar_conflicts[0]
            confidence = most_common['frequency'] / sum(c['frequency'] for c in similar_conflicts)
            
            return {
                'suggested_strategy': most_common['strategy'],
                'confidence': confidence,
                'reason': f'基于您过去{most_common["frequency"]}次类似选择'
            }
        
        return None
```

### 6. 性能优化

#### 冲突检测优化
```python
class OptimizedConflictDetector:
    def __init__(self):
        self.conflict_cache = {}
        self.last_check_time = {}
        
    async def quick_conflict_check(self, record_id, local_version):
        """快速冲突检查"""
        
        # 检查缓存
        cache_key = f"{record_id}:{local_version}"
        if cache_key in self.conflict_cache:
            cache_entry = self.conflict_cache[cache_key]
            if (datetime.now() - cache_entry['timestamp']).seconds < 30:
                return cache_entry['result']
        
        # 只检查版本号和时间戳
        remote_info = await self.get_remote_record_info(record_id)
        
        has_conflict = (
            remote_info['version'] != local_version or
            remote_info['updated_at'] > self.last_check_time.get(record_id, datetime.min)
        )
        
        # 更新缓存
        self.conflict_cache[cache_key] = {
            'result': has_conflict,
            'timestamp': datetime.now()
        }
        
        return has_conflict
        
    async def batch_conflict_check(self, record_list):
        """批量冲突检查"""
        
        # 构建批量查询
        record_ids = [r['id'] for r in record_list]
        
        remote_versions = await self.get_remote_versions_batch(record_ids)
        
        conflicts = []
        for record in record_list:
            remote_version = remote_versions.get(record['id'])
            if remote_version and remote_version != record['sync_version']:
                conflicts.append({
                    'record_id': record['id'],
                    'local_version': record['sync_version'],
                    'remote_version': remote_version
                })
        
        return conflicts
```

## 📊 方案优势

1. **实时检测**: WebSocket实时监听并发编辑
2. **智能合并**: 字段级自动合并减少用户干预
3. **直观界面**: 清晰的冲突对比和解决界面
4. **冲突预防**: 编辑锁和协作提示机制
5. **学习能力**: 基于历史记录智能建议
6. **性能优化**: 缓存和批量处理提升效率

## 🎯 适用场景

- ✅ 多设备同时编辑
- ✅ 团队协作场景
- ✅ 网络不稳定环境
- ✅ 高频修改操作

这个方案专门针对多设备并发修改冲突场景，提供了完整的检测、预防、解决和学习机制。
