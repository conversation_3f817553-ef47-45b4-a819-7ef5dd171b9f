# 场景6：数据备份与恢复

## 📱 场景描述

**用户情况**：
- 需要定期备份重要数据到云端
- 设备损坏、丢失或系统崩溃后需要恢复数据
- 误删除数据后需要找回
- 需要将数据迁移到新设备
- 合规要求需要保留历史数据

**典型场景**：
- 设备硬件故障导致数据丢失
- 误操作删除重要资产信息
- 系统升级或重装后数据恢复
- 从旧设备迁移到新设备
- 审计要求查看历史数据状态

**核心需求**：
1. 自动定期备份数据到云端
2. 支持增量备份和全量备份
3. 快速恢复数据到指定时间点
4. 提供数据版本管理和历史查看
5. 确保备份数据的完整性和安全性

## 🎯 解决方案

### 1. 自动备份机制

#### 备份策略配置
```python
class BackupManager:
    def __init__(self, db, config):
        self.db = db
        self.config = config
        self.backup_strategies = {
            'full': self.create_full_backup,
            'incremental': self.create_incremental_backup,
            'differential': self.create_differential_backup
        }
        
    async def configure_backup_schedule(self):
        """配置备份计划"""
        
        backup_config = {
            'full_backup': {
                'frequency': 'weekly',        # 每周全量备份
                'time': '02:00',             # 凌晨2点
                'day': 'sunday',             # 周日
                'retention': 12              # 保留12个全量备份
            },
            'incremental_backup': {
                'frequency': 'daily',        # 每日增量备份
                'time': '01:00',             # 凌晨1点
                'retention': 30              # 保留30天增量备份
            },
            'real_time_backup': {
                'enabled': True,             # 启用实时备份
                'batch_size': 100,           # 批量大小
                'interval': 300              # 5分钟间隔
            }
        }
        
        # 注册定时任务
        await self.schedule_backup_tasks(backup_config)
        
    async def schedule_backup_tasks(self, config):
        """安排备份任务"""
        
        # 全量备份任务
        full_backup_cron = self.build_cron_expression(config['full_backup'])
        asyncio.create_task(self.schedule_cron_task(
            full_backup_cron, 
            lambda: self.create_full_backup()
        ))
        
        # 增量备份任务
        incremental_cron = self.build_cron_expression(config['incremental_backup'])
        asyncio.create_task(self.schedule_cron_task(
            incremental_cron,
            lambda: self.create_incremental_backup()
        ))
        
        # 实时备份任务
        if config['real_time_backup']['enabled']:
            asyncio.create_task(self.start_real_time_backup(
                config['real_time_backup']
            ))
```

#### 全量备份实现
```python
async def create_full_backup(self):
    """创建全量备份"""
    
    backup_id = f"full_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        await self.update_backup_progress("正在准备全量备份...", 0)
        
        # 1. 获取所有表的数据
        tables_data = {}
        tables = ['t_assets', 't_other_table']  # 需要备份的表
        
        for i, table_name in enumerate(tables):
            await self.update_backup_progress(f"正在备份表 {table_name}...", (i/len(tables))*80)
            
            table_data = await self.export_table_data(table_name)
            tables_data[table_name] = table_data
        
        # 2. 创建备份元数据
        backup_metadata = {
            'backup_id': backup_id,
            'backup_type': 'full',
            'created_at': datetime.now().isoformat(),
            'device_id': self.device_id,
            'tables': list(tables_data.keys()),
            'total_records': sum(len(data) for data in tables_data.values()),
            'schema_version': await self.get_schema_version(),
            'app_version': self.get_app_version()
        }
        
        # 3. 压缩和加密备份数据
        await self.update_backup_progress("正在压缩和加密备份数据...", 85)
        
        backup_package = {
            'metadata': backup_metadata,
            'data': tables_data
        }
        
        compressed_data = await self.compress_backup_data(backup_package)
        encrypted_data = await self.encrypt_backup_data(compressed_data)
        
        # 4. 上传到云端
        await self.update_backup_progress("正在上传备份到云端...", 90)
        
        upload_result = await self.upload_backup_to_cloud(backup_id, encrypted_data)
        
        # 5. 记录备份信息
        await self.record_backup_info(backup_metadata, upload_result)
        
        await self.update_backup_progress("全量备份完成", 100)
        
        return {
            'success': True,
            'backup_id': backup_id,
            'size': len(encrypted_data),
            'records_count': backup_metadata['total_records']
        }
        
    except Exception as e:
        await self.handle_backup_error('full_backup', e)
        raise

async def export_table_data(self, table_name):
    """导出表数据"""
    
    # 分页导出大表数据
    page_size = 1000
    offset = 0
    all_data = []
    
    while True:
        sql = f"""
        SELECT * FROM {table_name} 
        ORDER BY id 
        LIMIT ? OFFSET ?
        """
        
        page_data = await self.db.fetch_all(sql, (page_size, offset))
        
        if not page_data:
            break
            
        # 处理敏感数据
        processed_data = [
            await self.process_record_for_backup(record) 
            for record in page_data
        ]
        
        all_data.extend(processed_data)
        offset += page_size
    
    return all_data
```

#### 增量备份实现
```python
async def create_incremental_backup(self):
    """创建增量备份"""
    
    backup_id = f"inc_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # 1. 获取上次备份时间
        last_backup_time = await self.get_last_backup_timestamp()
        
        await self.update_backup_progress("正在检测数据变更...", 10)
        
        # 2. 检测变更数据
        changes = await self.detect_changes_since(last_backup_time)
        
        if not self.has_significant_changes(changes):
            return {'success': True, 'message': '无需要备份的变更'}
        
        # 3. 创建增量备份数据
        await self.update_backup_progress("正在创建增量备份...", 50)
        
        incremental_data = {
            'backup_id': backup_id,
            'backup_type': 'incremental',
            'base_backup_time': last_backup_time,
            'created_at': datetime.now().isoformat(),
            'changes': changes
        }
        
        # 4. 压缩和上传
        await self.update_backup_progress("正在上传增量备份...", 80)
        
        compressed_data = await self.compress_backup_data(incremental_data)
        encrypted_data = await self.encrypt_backup_data(compressed_data)
        
        upload_result = await self.upload_backup_to_cloud(backup_id, encrypted_data)
        
        await self.record_backup_info(incremental_data, upload_result)
        
        await self.update_backup_progress("增量备份完成", 100)
        
        return {
            'success': True,
            'backup_id': backup_id,
            'changes_count': sum(len(table_changes) for table_changes in changes.values())
        }
        
    except Exception as e:
        await self.handle_backup_error('incremental_backup', e)
        raise

async def detect_changes_since(self, since_time):
    """检测指定时间以来的变更"""
    
    changes = {}
    tables = ['t_assets', 't_other_table']
    
    for table_name in tables:
        table_changes = {
            'inserts': [],
            'updates': [],
            'deletes': []
        }
        
        # 新增记录
        inserts = await self.db.fetch_all(f"""
            SELECT * FROM {table_name} 
            WHERE created_at > ?
        """, (since_time,))
        table_changes['inserts'] = inserts
        
        # 修改记录
        updates = await self.db.fetch_all(f"""
            SELECT * FROM {table_name} 
            WHERE updated_at > ? AND created_at <= ?
        """, (since_time, since_time))
        table_changes['updates'] = updates
        
        # 删除记录
        deletes = await self.db.fetch_all(f"""
            SELECT * FROM {table_name} 
            WHERE is_deleted = 1 AND updated_at > ?
        """, (since_time,))
        table_changes['deletes'] = deletes
        
        changes[table_name] = table_changes
    
    return changes
```

### 2. 数据恢复机制

#### 恢复向导界面
```python
class DataRecoveryWizard:
    async def start_recovery_wizard(self):
        """启动数据恢复向导"""
        
        # 1. 显示恢复选项
        recovery_options = await self.show_recovery_options()
        
        if recovery_options['type'] == 'point_in_time':
            await self.point_in_time_recovery(recovery_options['target_time'])
        elif recovery_options['type'] == 'specific_backup':
            await self.specific_backup_recovery(recovery_options['backup_id'])
        elif recovery_options['type'] == 'selective_recovery':
            await self.selective_recovery(recovery_options['selection'])
        
    async def show_recovery_options(self):
        """显示恢复选项界面"""
        
        # 获取可用备份列表
        available_backups = await self.get_available_backups()
        
        recovery_ui = f"""
        ┌─────────────────────────────────────────────────────────────┐
        │                    数据恢复向导                              │
        ├─────────────────────────────────────────────────────────────┤
        │                                                             │
        │  请选择恢复方式：                                            │
        │                                                             │
        │  ○ 恢复到指定时间点                                          │
        │    选择具体的日期和时间进行恢复                              │
        │                                                             │
        │  ○ 从特定备份恢复                                            │
        │    从备份列表中选择一个备份进行恢复                          │
        │                                                             │
        │  ○ 选择性恢复                                                │
        │    只恢复特定的表或记录                                      │
        │                                                             │
        │  可用备份: {len(available_backups)} 个                       │
        │  最新备份: {available_backups[0]['created_at'] if available_backups else '无'}  │
        │                                                             │
        │  [ 下一步 ]  [ 取消 ]                                       │
        └─────────────────────────────────────────────────────────────┘
        """
        
        return await self.show_recovery_dialog(recovery_ui, available_backups)
```

#### 时间点恢复
```python
async def point_in_time_recovery(self, target_time):
    """时间点恢复"""
    
    try:
        await self.update_recovery_progress("正在分析恢复路径...", 10)
        
        # 1. 找到最佳的恢复路径
        recovery_path = await self.find_optimal_recovery_path(target_time)
        
        # 2. 验证恢复路径的完整性
        path_valid = await self.validate_recovery_path(recovery_path)
        
        if not path_valid:
            raise RecoveryError("无法找到完整的恢复路径")
        
        # 3. 备份当前数据
        await self.update_recovery_progress("正在备份当前数据...", 20)
        current_backup = await self.create_pre_recovery_backup()
        
        # 4. 执行恢复
        await self.update_recovery_progress("正在执行数据恢复...", 40)
        
        # 从基础全量备份开始
        base_backup = recovery_path['base_backup']
        await self.restore_from_full_backup(base_backup)
        
        # 应用增量备份
        for incremental in recovery_path['incremental_backups']:
            await self.apply_incremental_backup(incremental)
            
        # 5. 验证恢复结果
        await self.update_recovery_progress("正在验证恢复结果...", 80)
        validation_result = await self.validate_recovery_result(target_time)
        
        if validation_result['success']:
            await self.update_recovery_progress("数据恢复完成", 100)
            return {
                'success': True,
                'restored_records': validation_result['record_count'],
                'target_time': target_time
            }
        else:
            # 恢复失败，回滚到备份
            await self.rollback_to_backup(current_backup)
            raise RecoveryError("恢复验证失败，已回滚到原始状态")
            
    except Exception as e:
        await self.handle_recovery_error(e, current_backup)
        raise

async def find_optimal_recovery_path(self, target_time):
    """找到最优的恢复路径"""
    
    # 1. 找到目标时间之前的最新全量备份
    base_backup = await self.find_base_backup_before_time(target_time)
    
    if not base_backup:
        raise RecoveryError("找不到合适的基础备份")
    
    # 2. 找到需要应用的增量备份
    incremental_backups = await self.find_incremental_backups_between(
        base_backup['created_at'], target_time
    )
    
    # 3. 计算恢复路径的成本
    path_cost = await self.calculate_recovery_cost(base_backup, incremental_backups)
    
    return {
        'base_backup': base_backup,
        'incremental_backups': incremental_backups,
        'estimated_time': path_cost['time'],
        'data_size': path_cost['size']
    }
```

#### 选择性恢复
```python
async def selective_recovery(self, selection_criteria):
    """选择性恢复"""
    
    try:
        # 1. 分析选择条件
        recovery_scope = await self.analyze_selection_criteria(selection_criteria)
        
        await self.update_recovery_progress("正在准备选择性恢复...", 10)
        
        # 2. 找到包含目标数据的备份
        relevant_backups = await self.find_backups_containing_data(recovery_scope)
        
        # 3. 提取目标数据
        await self.update_recovery_progress("正在提取目标数据...", 40)
        
        extracted_data = {}
        for backup in relevant_backups:
            backup_data = await self.extract_selective_data(backup, recovery_scope)
            extracted_data.update(backup_data)
        
        # 4. 显示恢复预览
        preview_result = await self.show_selective_recovery_preview(extracted_data)
        
        if not preview_result['confirmed']:
            return {'success': False, 'message': '用户取消恢复'}
        
        # 5. 应用选择性恢复
        await self.update_recovery_progress("正在应用选择性恢复...", 70)
        
        recovery_result = await self.apply_selective_recovery(
            extracted_data, 
            preview_result['merge_strategy']
        )
        
        await self.update_recovery_progress("选择性恢复完成", 100)
        
        return recovery_result
        
    except Exception as e:
        await self.handle_recovery_error(e)
        raise

async def show_selective_recovery_preview(self, extracted_data):
    """显示选择性恢复预览"""
    
    preview_ui = f"""
    ┌─────────────────────────────────────────────────────────────┐
    │                  选择性恢复预览                              │
    ├─────────────────────────────────────────────────────────────┤
    │                                                             │
    │  将要恢复的数据：                                            │
    │                                                             │
    │  表名          │  记录数  │  最后修改时间                    │
    │  ─────────────────────────────────────────────────────────  │
    │  t_assets      │  {len(extracted_data.get('t_assets', []))}     │  2024-01-15 10:30:00     │
    │  t_other       │  {len(extracted_data.get('t_other', []))}      │  2024-01-15 09:15:00     │
    │                                                             │
    │  冲突处理策略：                                              │
    │  ○ 覆盖现有数据                                              │
    │  ○ 合并数据（保留新字段）                                    │
    │  ○ 跳过冲突记录                                              │
    │                                                             │
    │  [ 确认恢复 ]  [ 修改选择 ]  [ 取消 ]                      │
    └─────────────────────────────────────────────────────────────┘
    """
    
    return await self.show_preview_dialog(preview_ui, extracted_data)
```

### 3. 备份完整性验证

#### 备份验证机制
```python
class BackupValidator:
    async def validate_backup_integrity(self, backup_id):
        """验证备份完整性"""
        
        validation_result = {
            'backup_id': backup_id,
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'validation_time': datetime.now()
        }
        
        try:
            # 1. 下载并解密备份
            backup_data = await self.download_and_decrypt_backup(backup_id)
            
            # 2. 验证备份元数据
            metadata_valid = await self.validate_backup_metadata(backup_data['metadata'])
            if not metadata_valid['valid']:
                validation_result['errors'].extend(metadata_valid['errors'])
                validation_result['is_valid'] = False
            
            # 3. 验证数据完整性
            data_valid = await self.validate_backup_data(backup_data['data'])
            if not data_valid['valid']:
                validation_result['errors'].extend(data_valid['errors'])
                validation_result['is_valid'] = False
            
            # 4. 验证数据一致性
            consistency_valid = await self.validate_data_consistency(backup_data)
            if not consistency_valid['valid']:
                validation_result['warnings'].extend(consistency_valid['warnings'])
            
            # 5. 验证可恢复性
            recovery_valid = await self.validate_recovery_capability(backup_data)
            if not recovery_valid['valid']:
                validation_result['errors'].extend(recovery_valid['errors'])
                validation_result['is_valid'] = False
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"验证过程异常: {str(e)}")
        
        # 记录验证结果
        await self.record_validation_result(validation_result)
        
        return validation_result

async def validate_backup_data(self, backup_data):
    """验证备份数据"""
    
    validation_result = {'valid': True, 'errors': []}
    
    for table_name, table_data in backup_data.items():
        # 验证表结构
        schema_valid = await self.validate_table_schema(table_name, table_data)
        if not schema_valid:
            validation_result['errors'].append(f"表 {table_name} 结构验证失败")
            validation_result['valid'] = False
        
        # 验证数据类型
        for record in table_data:
            type_valid = await self.validate_record_types(table_name, record)
            if not type_valid['valid']:
                validation_result['errors'].append(
                    f"表 {table_name} 记录 {record.get('id', 'unknown')} 数据类型错误: {type_valid['error']}"
                )
                validation_result['valid'] = False
        
        # 验证约束条件
        constraints_valid = await self.validate_table_constraints(table_name, table_data)
        if not constraints_valid['valid']:
            validation_result['errors'].extend(constraints_valid['errors'])
            validation_result['valid'] = False
    
    return validation_result
```

### 4. 备份存储管理

#### 云端存储策略
```python
class CloudBackupStorage:
    def __init__(self, config):
        self.config = config
        self.storage_providers = {
            'primary': self.config['primary_storage'],
            'secondary': self.config.get('secondary_storage'),
            'archive': self.config.get('archive_storage')
        }
        
    async def upload_backup_with_redundancy(self, backup_id, backup_data):
        """冗余上传备份"""
        
        upload_results = {}
        
        # 主存储上传
        try:
            primary_result = await self.upload_to_primary_storage(backup_id, backup_data)
            upload_results['primary'] = primary_result
        except Exception as e:
            upload_results['primary'] = {'success': False, 'error': str(e)}
        
        # 备用存储上传
        if self.storage_providers['secondary']:
            try:
                secondary_result = await self.upload_to_secondary_storage(backup_id, backup_data)
                upload_results['secondary'] = secondary_result
            except Exception as e:
                upload_results['secondary'] = {'success': False, 'error': str(e)}
        
        # 检查上传结果
        if not upload_results['primary']['success'] and not upload_results.get('secondary', {}).get('success'):
            raise BackupError("所有存储位置上传失败")
        
        return upload_results

async def manage_backup_lifecycle(self):
    """管理备份生命周期"""
    
    # 1. 清理过期备份
    await self.cleanup_expired_backups()
    
    # 2. 归档旧备份
    await self.archive_old_backups()
    
    # 3. 验证备份完整性
    await self.periodic_backup_validation()
    
    # 4. 优化存储空间
    await self.optimize_storage_space()

async def cleanup_expired_backups(self):
    """清理过期备份"""
    
    retention_policies = {
        'full': 90,        # 全量备份保留90天
        'incremental': 30, # 增量备份保留30天
        'archive': 365     # 归档备份保留1年
    }
    
    for backup_type, retention_days in retention_policies.items():
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        expired_backups = await self.find_expired_backups(backup_type, cutoff_date)
        
        for backup in expired_backups:
            try:
                await self.delete_backup(backup['backup_id'])
                await self.log_backup_deletion(backup)
            except Exception as e:
                await self.log_error(f"删除备份失败: {backup['backup_id']}, 错误: {e}")
```

## 📊 方案优势

1. **自动化**: 定时自动备份，无需人工干预
2. **多策略**: 支持全量、增量、实时备份
3. **完整性**: 备份验证确保数据可靠性
4. **灵活恢复**: 支持时间点、选择性恢复
5. **冗余存储**: 多地备份确保数据安全
6. **生命周期**: 自动管理备份存储和清理

## 🎯 适用场景

- ✅ 重要数据保护
- ✅ 灾难恢复准备
- ✅ 合规审计要求
- ✅ 设备迁移场景
- ✅ 误操作恢复

这个方案专门针对数据备份与恢复场景，提供了企业级的数据保护和恢复能力。
